"use client";
import { useState } from "react";
import { Row, Col, Container } from "react-bootstrap";
import Image from "next/image";
import { GetADemoModal } from "@/components/ui";
import AnimatedButton from "@/components/common/AnimatedButton";
import ScrollReveal from "@/components/common/ScrollReveal";

const HomeBanner = () => {
  const [showModal, setShowModal] = useState(false);

  const openModal = () => {
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
  };
  return (
    <div className="homeb-banner-section p-relative">
      <video
        id="background-video"
        autoPlay
        loop
        muted
        playsInline
        preload="auto"
      >
        <source src="/images/becomap-video.mp4" type="video/mp4" />
        <source src="/images/becomap-video.webm" type="video/webm" />
      </video>
      <Container fluid={"xl"} className="banner-content">
        <ScrollReveal direction="up" delay={0.2}>
          <h1 className="banner-title">
            Indoor Navigation & Wayfinding Solution
          </h1>
        </ScrollReveal>
        {/* <p>Powerful Indoor navigation and location services for built-up areas; we help
                        you gain more control and visibility while providing convenience through our solutions.</p> */}
        <ScrollReveal direction="up" delay={0.4}>
          <div className="button-wrap" style={{ zIndex: "3" , position:"relative"}}>
            <AnimatedButton variant="primary" className="btn-secondary" onClick={openModal}>
              Get a Demo
            </AnimatedButton>
          </div>
        </ScrollReveal>
      </Container>
      <GetADemoModal
        isOpen={showModal}
        onClose={() => closeModal()}
        hitUrl={"home"}
      />
    </div>
  );
};

export default HomeBanner;
