// Custom hooks for animations
import { useInView } from 'framer-motion';
import { useRef, useEffect, useState } from 'react';

/**
 * Hook for scroll-triggered animations
 * @param {Object} options - Intersection observer options
 * @returns {Object} - ref and inView state
 */
export const useScrollAnimation = (options = {}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: "-100px",
    ...options
  });

  return { ref, isInView };
};

/**
 * Hook for staggered animations
 * @param {number} itemCount - Number of items to stagger
 * @param {number} delay - Delay between items
 * @returns {Object} - Animation controls
 */
export const useStaggerAnimation = (itemCount, delay = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef(null);
  
  const isInView = useInView(ref, {
    once: true,
    margin: "-50px"
  });

  useEffect(() => {
    if (isInView) {
      setIsVisible(true);
    }
  }, [isInView]);

  const getItemDelay = (index) => index * delay;

  return { ref, isVisible, getItemDelay };
};

/**
 * Hook for reduced motion preference
 * @returns {boolean} - Whether user prefers reduced motion
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

/**
 * Hook for hover animations
 * @returns {Object} - Hover state and handlers
 */
export const useHoverAnimation = () => {
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);

  return {
    isHovered,
    hoverProps: {
      onMouseEnter: handleMouseEnter,
      onMouseLeave: handleMouseLeave
    }
  };
};

/**
 * Hook for parallax effect
 * @param {number} speed - Parallax speed (0-1)
 * @returns {Object} - Transform value and ref
 */
export const useParallax = (speed = 0.5) => {
  const [offsetY, setOffsetY] = useState(0);
  const ref = useRef(null);

  useEffect(() => {
    const handleScroll = () => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * -speed;
        setOffsetY(rate);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return { ref, transform: `translateY(${offsetY}px)` };
};

/**
 * Hook for typewriter effect
 * @param {string} text - Text to type
 * @param {number} speed - Typing speed in ms
 * @returns {string} - Current displayed text
 */
export const useTypewriter = (text, speed = 100) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  const reset = () => {
    setDisplayText('');
    setCurrentIndex(0);
  };

  return { displayText, reset, isComplete: currentIndex === text.length };
};

/**
 * Hook for counting animation
 * @param {number} end - End number
 * @param {number} duration - Animation duration in ms
 * @param {number} start - Start number
 * @returns {number} - Current count
 */
export const useCountUp = (end, duration = 2000, start = 0) => {
  const [count, setCount] = useState(start);
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (!isActive) return;

    const increment = (end - start) / (duration / 16); // 60fps
    let current = start;

    const timer = setInterval(() => {
      current += increment;
      if (current >= end) {
        setCount(end);
        clearInterval(timer);
      } else {
        setCount(Math.floor(current));
      }
    }, 16);

    return () => clearInterval(timer);
  }, [end, duration, start, isActive]);

  const startCounting = () => setIsActive(true);
  const reset = () => {
    setCount(start);
    setIsActive(false);
  };

  return { count, startCounting, reset };
};

/**
 * Hook for scroll progress
 * @returns {number} - Scroll progress (0-1)
 */
export const useScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = window.pageYOffset / totalHeight;
      setScrollProgress(Math.min(Math.max(progress, 0), 1));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return scrollProgress;
};
