import React, { useRef, useState } from "react";
import { formSubmit } from "@/app/action/action";
import { Container, Row, Col, Form } from "react-bootstrap";
import { Formik } from "formik";
import { useRouter } from "next/navigation";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";
import AnimatedInput from "@/components/common/AnimatedInput";
import AnimatedButton from "@/components/common/AnimatedButton";
import ScrollReveal from "@/components/common/ScrollReveal";
import LoadingSpinner from "@/components/common/LoadingSpinner";

const ContactForm = () => {
  const router = useRouter();
  const [phone, setPhone] = useState("");
  const [formStatus, setFormStatus] = useState(false);

  return (
    <div className="contact-wrap contact-form-wrap">
      <Container className="contact-container">
        <div className="client-feedback-right flex-1">
          <ScrollReveal direction="up" delay={0.2}>
            <div className="section-head">
              <h2 className="main-title mb-xl-4">
                Ready to unlock the power of location?
              </h2>
              <p>
                Discover the power of indoor positioning and tracking - contact us
                today!
              </p>
            </div>
          </ScrollReveal>
          <div className="talkto-form-wrap">
            <Formik
              initialValues={{
                name: "",
                email: "",
                phone_number: "",
                option: "",
                message: "",
              }}
              validate={(values) => {
                const errors = {};
                if (!values.name) errors.name = "Name is required";
                if (!values.email) {
                  errors.email = "Email is required";
                } else if (
                  !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email)
                ) {
                  errors.email = "Invalid email address";
                }
                if (!values.phone_number || values.phone_number.length < 10) {
                  errors.phone_number = "Valid phone number is required";
                }
                if (!values.option) errors.option = "Please select an option";
                if (!values.message) errors.message = "Message is required";
                return errors;
              }}
              onSubmit={async (values, { setSubmitting, resetForm }) => {
                try {
                  const status = await formSubmit(values, "contact-sales");
                  if (status === 201) {
                    setFormStatus(true); // Set form status to submitted
                    router.push("/thankyou");
                    setTimeout(() => {
                      setFormStatus(false);
                      setSubmitting(false);
                      resetForm();
                    }, 5000);
                  } else {
                    console.error(
                      `Form submission failed with status: ${status}`
                    );
                  }
                } catch (error) {
                  console.error(`Form submission failed: ${error.message}`);
                }
              }}
            >
              {({
                values,
                errors,
                touched,
                handleChange,
                handleBlur,
                handleSubmit,
                isSubmitting,
                setFieldValue,
              }) => (
                <ScrollReveal direction="up" delay={0.4}>
                  <Form onSubmit={handleSubmit}>
                    <Row className="talkto-form-row">
                      <Col md={12}>
                        <Form.Group className="talkto-form-item">
                          <AnimatedInput
                            type="text"
                            name="name"
                            placeholder="Name"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.name}
                            isInvalid={errors.name && touched.name}
                            error={errors.name && touched.name ? errors.name : ""}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="talkto-form-item">
                          <AnimatedInput
                            type="email"
                            name="email"
                            placeholder="Email Address*"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.email}
                            isInvalid={errors.email && touched.email}
                            error={errors.email && touched.email ? errors.email : ""}
                          />
                        </Form.Group>
                      <Col md={6}>
                        <Form.Group className="talkto-form-item">
                          <PhoneInput
                            inputProps={{
                              name: "phone_number",
                              onBlur: handleBlur,
                              className: "form-control w-100",
                            }}
                            placeholder="Phone Number*"
                            country={"us"}
                            enableSearch
                            onChange={(phone) =>
                              setFieldValue("phone_number", phone)
                            }
                            value={phone}
                          />
                          {errors.phone_number && touched.phone_number && (
                            <div className="invalid-feedback d-block">
                              {errors.phone_number}
                            </div>
                          )}
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Form.Group className="talkto-form-item p-relative">
                          <AnimatedInput
                            as="select"
                            name="option"
                            value={values.option}
                            onChange={(e) =>
                              setFieldValue("option", e.target.value)
                            }
                            onBlur={handleBlur}
                            isInvalid={touched.option && !!errors.option}
                            error={errors.option && touched.option ? errors.option : ""}
                          >
                            <option value="">Your inquiry*</option>
                            <option value="Indoor navigation">
                              Indoor navigation
                            </option>
                            <option value="Asset tracking">Asset tracking</option>
                            <option value="General Inquiry">
                              General Inquiry
                            </option>
                          </AnimatedInput>
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Form.Group className="talkto-form-item">
                          <AnimatedInput
                            as="textarea"
                            name="message"
                            rows={3}
                            placeholder="Project Description"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.message}
                            isInvalid={errors.message && touched.message}
                            error={errors.message && touched.message ? errors.message : ""}
                          />
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <AnimatedButton
                          variant="dark"
                          type="submit"
                          className={`form-btn ${isSubmitting ? "loading" : ""}`}
                          disabled={isSubmitting}
                          loading={isSubmitting}
                        >
                          {isSubmitting ? (
                            <LoadingSpinner size="sm" />
                          ) : formStatus ? (
                            <span>Submitted</span>
                          ) : (
                            <span>Talk to our Team</span>
                          )}
                        </AnimatedButton>
                      </Col>
                    </Row>
                  </Form>
                </ScrollReveal>
              )}
            </Formik>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ContactForm;
