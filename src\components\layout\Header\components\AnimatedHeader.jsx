"use client";
import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState } from 'react';
import Container from 'react-bootstrap/Container';
import Image from 'next/image';
import Link from 'next/link';
import Headermenu from "./headermenu";
import HeaderSearch from "./headersearch";
import { useReducedMotion } from '@/lib/animations';

const AnimatedHeader = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const handleScroll = () => {
    const scrollPosition = window.scrollY;
    setIsSticky(scrollPosition > 50);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "";
    return () => {
      document.body.style.overflow = "";
    };
  }, [menuOpen]);

  // Animation variants
  const headerVariants = {
    normal: {
      backgroundColor: "rgba(255, 255, 255, 0)",
      backdropFilter: "blur(0px)",
      boxShadow: "0px 0px 0px rgba(51, 55, 59, 0)",
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    },
    sticky: {
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      backdropFilter: "blur(10px)",
      boxShadow: "0px 5px 20px rgba(51, 55, 59, 0.1)",
      y: 0,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    }
  };

  const logoVariants = {
    normal: {
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    },
    sticky: {
      scale: 0.9,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    }
  };

  const containerVariants = {
    normal: {
      paddingTop: "15px",
      paddingBottom: "15px",
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    },
    sticky: {
      paddingTop: "10px",
      paddingBottom: "10px",
      transition: {
        duration: prefersReducedMotion ? 0 : 0.4,
        ease: "easeOut"
      }
    }
  };

  const searchOverlayVariants = {
    hidden: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.2,
        ease: "easeIn"
      }
    },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: prefersReducedMotion ? 0 : 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      className={`header-wrap ${isSticky ? 'sticky' : ''} ${menuOpen ? 'menu-open' : ''}`}
      variants={headerVariants}
      initial="normal"
      animate={isSticky ? "sticky" : "normal"}
      style={{
        position: isSticky ? 'fixed' : 'relative',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        width: '100%'
      }}
    >
      <Container fluid={"xl"}>
        <motion.div 
          className="d-flex header-cont justify-content-between align-items-center"
          variants={containerVariants}
          initial="normal"
          animate={isSticky ? "sticky" : "normal"}
        >
          <div className="header-left">
            <Link href="/">
              <motion.div
                variants={logoVariants}
                initial="normal"
                animate={isSticky ? "sticky" : "normal"}
              >
                <Image
                  src="/images/logo.png"
                  alt="beco logo"
                  width={159}
                  height={28}
                  className="beco-logo"
                />
              </motion.div>
            </Link>
          </div>
          
          <div className="header-section-wrap d-flex align-items-center">
            <motion.div 
              className={`header-menu d-flex order-lg-1 order-2 ${isSearchOpen ? 'menu-hidden' : ''}`}
              initial={{ opacity: 1 }}
              animate={{ 
                opacity: isSearchOpen ? 0 : 1,
                transition: {
                  duration: prefersReducedMotion ? 0 : 0.3,
                  ease: "easeInOut"
                }
              }}
            >
              <Headermenu menuOpen={menuOpen} setMenuOpen={setMenuOpen} />
            </motion.div>

            <div className="header-right d-flex order-lg-2 order-1">
              <motion.div
                whileHover={prefersReducedMotion ? {} : { scale: 1.02 }}
                whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
                transition={{ duration: 0.2 }}
              >
                <Link href="/contact" className="border-btn medium-btn header-right-child">
                  Contact us
                </Link>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </Container>

      {/* Search Overlay */}
      <AnimatePresence>
        {isSearchOpen && (
          <motion.div
            variants={searchOverlayVariants}
            initial="hidden"
            animate="visible"
            exit="hidden"
            className="search-overlay-container"
            style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              right: 0,
              backgroundColor: 'white',
              padding: '20px',
              boxShadow: '0 5px 20px rgba(0,0,0,0.1)',
              zIndex: 1000
            }}
          >
            <HeaderSearch isSearchOpen={isSearchOpen} toggleSearch={toggleSearch} />
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default AnimatedHeader;
