"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "react-bootstrap";
import Image from "next/image";

import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import { Autoplay, EffectCreative, Pagination } from "swiper/modules";
import { useState } from "react";
import ScrollReveal from "@/components/common/ScrollReveal";
import StaggeredReveal from "@/components/common/StaggeredReveal";

const Technologys = [
  {
    title: "Beacons",
    description:
      "Beacons are low cost pieces of hardware, small enough to attach to the wall or countertop that utilize battery friendly Bluetooth connection (BLE/Bluetooth Smart) to transmit messages directly to a smartphone or tablet to get appropriate notifications.",
    iconUrl: "/images/icons/beacons-icon.png",
    iconClass: "beacons-icon-bg",
  },
  {
    title: "Geo-fencing",
    description:
      "By using Gps technology we set a virtual geographical boundary for a real area and equip software to trigger a response and enable functionalities to device when its presence is identified within the defined limits.",
    iconUrl: "/images/icons/geo-icon.png",
    iconClass: "geo-fencing-icon-bg",
  },
  {
    title: "Intuitive front-end",
    description:
      "From JavaScript to the latest React native framework, we deliver front-end solutions that work best for you.",
    iconUrl: "/images/icons/frontend-icon.png",
    iconClass: "frontend-icon-bg",
  },

  {
    title: "Location data hardware’s",
    description:
      "Several location data providing hardware such as RFID, GPS tags are used based on the use-case to deliver location services that make your life easier.",
    iconUrl: "/images/icons/data-hd-icon.png",
    iconClass: "location-data-icon-bg",
  },
  {
    title: "Python based application",
    description:
      "Our services are built on the trusted and efficient Python programming language. This guarantees quick responses and easy customization.",
    iconUrl: "/images/icons/python-icon.png",
    iconClass: "python-app-icon-bg",
  },

  //   Add more service items as needed
];

const TechnologyWeUse = () => {
  const [swiperRef, setSwiperRef] = useState(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const slideTo = (index) => {
    swiperRef.slideTo(index - 1, 0);
    setActiveIndex(index - 1);
  };
  return (
    <div className="section our-technology-section">
      <Container fluid={"xl"}>
        <Row className="align-items-center justify-content-center mb-5">
          <Col md={8}>
            <ScrollReveal direction="up" delay={0.2}>
              <div className="section-head text-center">
                <h2 className="main-title mb-2 text-white">
                  Technologies We Use
                </h2>
              </div>
            </ScrollReveal>
          </Col>
        </Row>
        <Row className="align-items-center justify-content-center gx-xl-5">
          <Col lg={6}>
            <ScrollReveal direction="left" delay={0.3}>
              <div className="technology-svg">
              <svg
                id="Layer_1"
                data-name="Layer 1"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 763 575"
              >
                <path
                  d="m763,555V98.776c0-11.046-8.954-20-20-20h-317.472c-5.173,0-10.146-2.005-13.873-5.594L341.472,5.594c-3.726-3.589-8.699-5.594-13.873-5.594H20C8.954,0,0,8.954,0,20v535c0,11.046,8.954,20,20,20h723c11.046,0,20-8.954,20-20Z"
                  fill="#202020"
                  strokeWidth="0"
                />
                <path
                  d="m219,112h119c11.046,0,20,8.954,20,20v56m0,0h102m-102,0v102m0,0h-82m82,0v100m0,0h108m-108,0v85c0,9.389-7.611,17-17,17h0"
                  fill="none"
                  stroke="#ffb82d"
                  strokeDasharray="0 0 5 5"
                  strokeWidth="2"
                />
                <path
                  d="m156,198v-33c0-2.761,2.239-5,5-5h43c2.761,0,5,2.239,5,5v33c0,2.761-2.239,5-5,5h-43c-2.761,0-5-2.239-5-5Zm468,293v-23c0-2.761,2.239-5,5-5h30c2.761,0,5,2.239,5,5v23c0,2.761-2.239,5-5,5h-30c-2.761,0-5-2.239-5-5Zm-390-64v-92c0-2.761,2.239-5,5-5h46c2.761,0,5,2.239,5,5v56c0,2.761,2.239,5,5,5h32c2.761,0,5,2.239,5,5v26c0,2.761-2.239,5-5,5h-88c-2.761,0-5-2.239-5-5Z"
                  fill="#34a853"
                  strokeWidth="0"
                />
                <path
                  d="m644,302v-55c0-2.761-2.239-5-5-5h-40.615c-2.761,0-5,2.239-5,5v9.009c0,2.762-2.238,5-5,5h-4.385c-2.761,0-5,2.239-5,5v35.991c0,2.761,2.239,5,5,5h55c2.761,0,5-2.239,5-5Zm-441-64v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Z"
                  fill="#ffb82d"
                  strokeWidth="0"
                />
                <path
                  d="m72,208v-43c0-2.761,2.239-5,5-5h37.5c2.761,0,5,2.239,5,5v5.5c0,2.761,2.239,5,5,5h3.5c2.761,0,5,2.239,5,5v27.5c0,2.761-2.239,5-5,5h-51c-2.761,0-5-2.239-5-5Zm309,288v-63c0-2.761,2.239-5,5-5h56.189c2.761,0,5,2.239,5,5v11.349c0,2.761,2.238,5,5,5h8.811c2.761,0,5,2.239,5,5v41.651c0,2.761-2.239,5-5,5h-75c-2.761,0-5-2.239-5-5Zm63-189v-34c0-2.761-2.239-5-5-5h-40.615c-2.761,0-5,2.239-5,5v2.868c0,2.761-2.238,5-5,5h-4.385c-2.761,0-5,2.238-5,5v21.132c0,2.761,2.239,5,5,5h55c2.761,0,5-2.239,5-5Zm-250,120v-34c0-2.761-2.239-5-5-5h-40.615c-2.761,0-5,2.239-5,5v2.868c0,2.761-2.238,5-5,5h-4.385c-2.761,0-5,2.238-5,5v21.132c0,2.761,2.239,5,5,5h55c2.761,0,5-2.239,5-5Zm23-229v-33c0-2.761,2.239-5,5-5h43c2.761,0,5,2.239,5,5v33c0,2.761-2.239,5-5,5h-43c-2.761,0-5-2.239-5-5Zm240,105v-73c0-2.761,2.239-5,5-5h43c2.761,0,5,2.239,5,5v73c0,2.761-2.239,5-5,5h-43c-2.761,0-5-2.239-5-5Zm0,41v-11c0-2.761,2.239-5,5-5h155c2.761,0,5,2.239,5,5v11c0,2.761-2.239,5-5,5h-155c-2.761,0-5-2.239-5-5Zm66-89v-23c0-2.761,2.239-5,5-5h30c2.761,0,5,2.239,5,5v23c0,2.761-2.239,5-5,5h-30c-2.761,0-5-2.239-5-5Zm132,28v-23c0-2.761,2.239-5,5-5h30c2.761,0,5,2.239,5,5v23c0,2.761-2.239,5-5,5h-30c-2.761,0-5-2.239-5-5Zm-132,20v-23c0-2.761,2.239-5,5-5h30c2.761,0,5,2.239,5,5v23c0,2.761-2.239,5-5,5h-30c-2.761,0-5-2.239-5-5Zm179-20v-23c0-2.761,2.239-5,5-5h30c2.761,0,5,2.239,5,5v23c0,2.761-2.239,5-5,5h-30c-2.761,0-5-2.239-5-5Zm8,150v58c0,2.761-2.239,5-5,5h-24.286c-2.761,0-5-2.239-5-5v-34c0-2.761-2.238-5-5-5h-15.714c-2.761,0-5-2.239-5-5v-14c0-2.761,2.239-5,5-5h50c2.761,0,5,2.239,5,5ZM156,238v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Zm24,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm47,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm7,220v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Zm47,0v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Zm-23,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm47,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm339-111v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Zm47,0v-13c0-1.105.895-2,2-2h11c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-11c-1.105,0-2-.895-2-2Zm-23,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm47,0v-13c0-1.105.895-2,2-2h10c1.105,0,2,.895,2,2v13c0,1.105-.895,2-2,2h-10c-1.105,0-2-.895-2-2Zm-437-149v-33c0-2.761,2.239-5,5-5h43c2.761,0,5,2.239,5,5v33c0,2.761-2.239,5-5,5h-43c-2.761,0-5-2.239-5-5Zm200,295v-33c0-2.761,2.239-5,5-5h80c2.761,0,5,2.239,5,5v33c0,2.761-2.239,5-5,5h-80c-2.761,0-5-2.239-5-5Zm-172.5-233h-2c-11.874,0-21.5-9.626-21.5-21.5s9.626-21.5,21.5-21.5h2c11.874,0,21.5,9.626,21.5,21.5s-9.626,21.5-21.5,21.5Zm90,104h-1c-8.56,0-15.5-6.94-15.5-15.5s6.94-15.5,15.5-15.5h1c8.56,0,15.5,6.94,15.5,15.5s-6.94,15.5-15.5,15.5Zm200,132h-1c-8.56,0-15.5-6.94-15.5-15.5s6.94-15.5,15.5-15.5h1c8.56,0,15.5,6.94,15.5,15.5s-6.94,15.5-15.5,15.5Z"
                  fill="#2f2f2f"
                  strokeWidth="0"
                />
                <g>
                  <rect
                    x="105"
                    y="93"
                    width="119"
                    height="40"
                    rx="8"
                    ry="8"
                    fill={activeIndex === 0 ? "#34A853" : "#fff"}
                    strokeWidth="0"
                    onClick={() => slideTo(1)}
                  />
                  <rect
                    x="457"
                    y="165"
                    width="155"
                    height="40"
                    rx="8"
                    ry="8"
                    fill={activeIndex === 1 ? "#34A853" : "#fff"}
                    strokeWidth="0"
                    onClick={() => slideTo(2)}
                  />
                  <rect
                    x="79"
                    y="268"
                    width="201"
                    height="40"
                    rx="8"
                    ry="8"
                    fill={activeIndex === 2 ? "#34A853" : "#fff"}
                    strokeWidth="0"
                    onClick={() => slideTo(3)}
                  />
                  <rect
                    x="79"
                    y="471"
                    width="259"
                    height="40"
                    rx="8"
                    ry="8"
                    fill={activeIndex === 4 ? "#34A853" : "#fff"}
                    strokeWidth="0"
                    onClick={() => slideTo(5)}
                  />
                  <rect
                    x="451"
                    y="370"
                    width="259"
                    height="40"
                    rx="8"
                    ry="8"
                    fill={activeIndex === 3 ? "#34A853" : "#fff"}
                    strokeWidth="0"
                    onClick={() => slideTo(4)}
                  />
                </g>
                <path
                  style={{ pointerEvents: "none" }}
                  d="m146.884,114.608c0-1.04-.992-1.792-2.384-1.792h-2.944v3.6h2.944c1.392,0,2.384-.768,2.384-1.808Zm-2.416-6.352h-2.912v3.056h2.912c1.232,0,2.096-.624,2.096-1.52s-.864-1.536-2.096-1.536Zm.352,9.744h-5.184v-11.328h5.12c2.192,0,3.744,1.28,3.744,3.04,0,1.104-.704,2.064-1.68,2.336,1.168.288,2,1.408,2,2.72,0,1.888-1.664,3.232-4,3.232Zm7.654-4.816h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm14.62,2.928c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm7.949-1.536c2.064,0,3.76,1.296,4.048,3.088l-1.664.208c-.256-1.056-1.248-1.808-2.368-1.808-1.44,0-2.576,1.216-2.576,2.896.016,1.68,1.12,2.896,2.576,2.896,1.152,0,2.096-.752,2.384-1.792l1.664.208c-.336,1.776-2.048,3.072-4.048,3.072-2.496,0-4.304-1.84-4.304-4.384s1.808-4.384,4.288-4.384Zm10.003,0c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm6.328-6.896h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm16.757,5.68c0,1.616-1.472,2.784-3.536,2.784-2.144,0-3.696-1.296-3.696-3.088h1.6c0,.96.88,1.664,2.096,1.664,1.088,0,1.872-.496,1.856-1.168,0-.848-1.264-1.056-2.08-1.168-1.904-.288-3.344-.528-3.344-2.464,0-1.488,1.424-2.576,3.264-2.576,2.096,0,3.632,1.168,3.632,2.784h-1.616c-.016-.8-.864-1.36-2.032-1.36-.88,0-1.616.48-1.616,1.136,0,.608.608.8,1.92,1.024,1.552.256,3.552.592,3.552,2.432Zm289.241,72.184c2.224,0,4-1.6,4.016-3.664h-4.096v-1.408h5.984c.304,3.904-2.176,6.736-5.936,6.736-3.584,0-6.16-2.544-6.16-6.064s2.576-6.064,6.16-6.064c2.656,0,4.912,1.68,5.44,4l-1.808.336c-.416-1.552-1.952-2.656-3.632-2.656-2.48,0-4.24,1.808-4.24,4.384,0,2.544,1.792,4.4,4.272,4.4Zm9.41-3.552h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm14.603-4.288c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm5.944-4.496h6.56v1.504h-6.56v-1.504Zm11.07-3.648v1.248h1.968v1.36h-1.968v6.768h-1.728v-6.768h-1.248v-1.36h1.248v-1.392c0-1.568.944-2.608,2.336-2.608.512,0,.944.08,1.36.224v1.456c-.272-.08-.528-.128-.832-.128-.672,0-1.12.48-1.136,1.2Zm5.144,4.56h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm10.587-3.968h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm13.685-.32c2.064,0,3.76,1.296,4.048,3.088l-1.664.208c-.256-1.056-1.248-1.808-2.368-1.808-1.44,0-2.576,1.216-2.576,2.896.016,1.68,1.12,2.896,2.576,2.896,1.152,0,2.096-.752,2.384-1.792l1.664.208c-.336,1.776-2.048,3.072-4.048,3.072-2.496,0-4.304-1.84-4.304-4.384s1.808-4.384,4.288-4.384Zm6.211.32h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm3.381,1.312h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm13.813,6.8c1.552,0,2.672-1.152,2.672-2.736,0-1.648-1.12-2.832-2.672-2.832s-2.688,1.184-2.688,2.832c0,1.584,1.136,2.736,2.688,2.736Zm.032,3.856c1.504,0,2.576-.976,2.576-2.688v-1.04c-.688.864-1.712,1.36-2.928,1.36-2.368,0-4.096-1.76-4.096-4.224,0-2.528,1.728-4.352,4.096-4.352,1.264,0,2.256.528,2.944,1.392v-1.104h1.728v7.984c0,2.624-1.776,4.144-4.256,4.144-2.128,0-3.888-1.344-4.176-3.216l1.6-.192c.272,1.12,1.328,1.936,2.512,1.936Zm-469.19,91.144h1.92v11.328h-1.92v-11.328Zm4.54,3.2h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm10.485,5.84v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm13.237,2.288h-1.712v-1.04c-.56.848-1.584,1.36-2.624,1.36-2,0-3.296-1.472-3.296-3.488v-4.96h1.744v4.832c0,1.2.704,2.064,1.936,2.064,1.2,0,2.224-.928,2.224-2.224v-4.672h1.728v8.128Zm2.533-8.128h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm4.085,7.152v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm5.989-5.84h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm8.085,9.44h-2.448l-2.976-8.128h1.856l2.336,6.784,2.352-6.784h1.872l-2.992,8.128Zm5.595-4.816h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm18.116-5.216v1.248h1.968v1.36h-1.968v6.768h-1.728v-6.768h-1.248v-1.36h1.248v-1.392c0-1.568.944-2.608,2.336-2.608.512,0,.944.08,1.36.224v1.456c-.272-.08-.528-.128-.832-.128-.672,0-1.12.48-1.136,1.2Zm3.721,1.248h1.68v1.04c.496-.832,1.28-1.328,2.208-1.328.32,0,.688.08,1.04.208v1.552c-.448-.192-.944-.256-1.264-.256-1.152,0-1.92,1.024-1.92,2.464v4.448h-1.744v-8.128Zm10.063-.32c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm6.327-6.896h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm10.486,5.84v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm4.896-3.44h6.56v1.504h-6.56v-1.504Zm9.902.912h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm10.587-3.968h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm13.813,1.312c-1.552,0-2.688,1.168-2.688,2.752,0,1.616,1.152,2.752,2.688,2.752,1.552,0,2.672-1.168,2.672-2.752s-1.12-2.752-2.672-2.752Zm-4.416,2.752c0-2.544,1.712-4.384,4.08-4.384,1.216,0,2.256.528,2.944,1.424v-4.784h1.744v11.808h-1.696v-1.136c-.704.912-1.744,1.456-2.992,1.456-2.352,0-4.08-1.84-4.08-4.384Zm-141.013,196.32h-2.208v4.688h2.208c1.728,0,2.976-.976,2.96-2.336,0-1.376-1.232-2.352-2.96-2.352Zm.304,6.288h-2.512v3.456h-1.856v-11.328h4.384c2.656,0,4.56,1.648,4.56,3.936s-1.904,3.936-4.576,3.936Zm6.484,6.8v-1.408c.352.128.752.224,1.072.224,1.12,0,1.456-1.12,1.872-2.128l-3.664-8.16h1.904l2.544,6,2.208-6h1.824l-3.184,8.32c-.704,1.872-1.424,3.424-3.152,3.424-.464,0-1.008-.096-1.424-.272Zm10.032-5.632v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm5.765-9.52h1.744v4.656c.544-.816,1.568-1.296,2.592-1.296,2.016,0,3.296,1.456,3.296,3.472v4.976h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-11.808Zm13.797,3.36c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm6.328-6.896h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm19.07,1.312c-1.552,0-2.672,1.168-2.672,2.752s1.12,2.752,2.672,2.752c1.536,0,2.688-1.136,2.688-2.752,0-1.584-1.136-2.752-2.688-2.752Zm4.416,2.752c0,2.544-1.728,4.384-4.08,4.384-1.248,0-2.288-.544-2.992-1.456v1.136h-1.696v-11.808h1.744v4.784c.688-.896,1.728-1.424,2.944-1.424,2.368,0,4.08,1.84,4.08,4.384Zm5.952,2.832c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm11.022,4.464c0,1.616-1.472,2.784-3.536,2.784-2.144,0-3.696-1.296-3.696-3.088h1.6c0,.96.88,1.664,2.096,1.664,1.088,0,1.872-.496,1.856-1.168,0-.848-1.264-1.056-2.08-1.168-1.904-.288-3.344-.528-3.344-2.464,0-1.488,1.424-2.576,3.264-2.576,2.096,0,3.632,1.168,3.632,2.784h-1.616c-.016-.8-.864-1.36-2.032-1.36-.88,0-1.616.48-1.616,1.136,0,.608.608.8,1.92,1.024,1.552.256,3.552.592,3.552,2.432Zm3.385-2.368h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm14.619-2.656c-1.552,0-2.688,1.168-2.688,2.752,0,1.616,1.152,2.752,2.688,2.752,1.552,0,2.672-1.168,2.672-2.752s-1.12-2.752-2.672-2.752Zm-4.416,2.752c0-2.544,1.712-4.384,4.08-4.384,1.216,0,2.256.528,2.944,1.424v-4.784h1.744v11.808h-1.696v-1.136c-.704.912-1.744,1.456-2.992,1.456-2.352,0-4.08-1.84-4.08-4.384Zm20.041,2.832c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm8.398,5.6c1.552,0,2.688-1.168,2.688-2.752,0-1.616-1.152-2.752-2.688-2.752-1.552,0-2.672,1.168-2.672,2.752s1.12,2.752,2.672,2.752Zm4.416-2.752c0,2.544-1.712,4.384-4.08,4.384-1.216,0-2.256-.528-2.944-1.424v4.784h-1.744v-11.808h1.696v1.136c.704-.912,1.744-1.456,2.992-1.456,2.352,0,4.08,1.84,4.08,4.384Zm6.271,2.752c1.552,0,2.688-1.168,2.688-2.752,0-1.616-1.152-2.752-2.688-2.752-1.552,0-2.672,1.168-2.672,2.752s1.12,2.752,2.672,2.752Zm4.416-2.752c0,2.544-1.712,4.384-4.08,4.384-1.216,0-2.256-.528-2.944-1.424v4.784h-1.744v-11.808h1.696v1.136c.704-.912,1.744-1.456,2.992-1.456,2.352,0,4.08,1.84,4.08,4.384Zm1.92-7.744h1.728v11.808h-1.728v-11.808Zm4.255,3.68h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm7.285.992c2.064,0,3.76,1.296,4.048,3.088l-1.664.208c-.256-1.056-1.248-1.808-2.368-1.808-1.44,0-2.576,1.216-2.576,2.896.016,1.68,1.12,2.896,2.576,2.896,1.152,0,2.096-.752,2.384-1.792l1.664.208c-.336,1.776-2.048,3.072-4.048,3.072-2.496,0-4.304-1.84-4.304-4.384s1.808-4.384,4.288-4.384Zm10.018,7.216c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm4.75,4.624v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm5.989-5.84h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm7.397.992c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm6.327-6.896h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm179.6-93.872h-7.264v-11.328h1.92v9.664h5.344v1.664Zm5.791-8.448c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm10.232-7.216c2.064,0,3.76,1.296,4.048,3.088l-1.664.208c-.256-1.056-1.248-1.808-2.368-1.808-1.44,0-2.576,1.216-2.576,2.896.016,1.68,1.12,2.896,2.576,2.896,1.152,0,2.096-.752,2.384-1.792l1.664.208c-.336,1.776-2.048,3.072-4.048,3.072-2.496,0-4.304-1.84-4.304-4.384s1.808-4.384,4.288-4.384Zm10.019,7.216c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm4.749,4.624v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm5.989-5.84h1.728v8.128h-1.728v-8.128Zm.864-1.312c-.64,0-1.088-.448-1.088-1.072,0-.608.448-1.04,1.088-1.04.624,0,1.072.432,1.072,1.04,0,.624-.448,1.072-1.072,1.072Zm7.397.992c2.592,0,4.416,1.808,4.416,4.384s-1.824,4.384-4.416,4.384c-2.608,0-4.416-1.808-4.416-4.384s1.808-4.384,4.416-4.384Zm0,7.216c1.552,0,2.688-1.184,2.688-2.832s-1.136-2.832-2.688-2.832c-1.568,0-2.672,1.184-2.672,2.832s1.104,2.832,2.672,2.832Zm6.328-6.896h1.712v1.04c.576-.848,1.584-1.36,2.64-1.36,1.984,0,3.28,1.472,3.28,3.488v4.96h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-8.128Zm18.75,1.312c-1.552,0-2.688,1.168-2.688,2.752,0,1.616,1.152,2.752,2.688,2.752,1.552,0,2.672-1.168,2.672-2.752s-1.12-2.752-2.672-2.752Zm-4.416,2.752c0-2.544,1.712-4.384,4.08-4.384,1.216,0,2.256.528,2.944,1.424v-4.784h1.744v11.808h-1.696v-1.136c-.704.912-1.744,1.456-2.992,1.456-2.352,0-4.08-1.84-4.08-4.384Zm15.104,2.832c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm4.749,4.624v-4.48h-1.248v-1.36h1.344l.224-1.968h1.424v1.968h1.984v1.36h-1.984v4.368c0,.736.448,1.248,1.184,1.248.224,0,.608-.048.912-.144v1.392c-.416.144-.992.224-1.44.224-1.456,0-2.4-1.056-2.4-2.608Zm9.797,1.056c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm8.983-4.896h1.744v4.656c.544-.816,1.568-1.296,2.592-1.296,2.016,0,3.296,1.456,3.296,3.472v4.976h-1.728v-4.832c0-1.2-.72-2.064-1.936-2.064s-2.224.928-2.224,2.224v4.672h-1.744v-11.808Zm13.814,10.576c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm4.045-1.216h1.68v1.04c.496-.832,1.28-1.328,2.208-1.328.32,0,.688.08,1.04.208v1.552c-.448-.192-.944-.256-1.264-.256-1.152,0-1.92,1.024-1.92,2.464v4.448h-1.744v-8.128Zm10.079,1.312c-1.552,0-2.688,1.168-2.688,2.752,0,1.616,1.152,2.752,2.688,2.752,1.552,0,2.672-1.168,2.672-2.752s-1.12-2.752-2.672-2.752Zm-4.416,2.752c0-2.544,1.712-4.384,4.08-4.384,1.216,0,2.256.528,2.944,1.424v-4.784h1.744v11.808h-1.696v-1.136c-.704.912-1.744,1.456-2.992,1.456-2.352,0-4.08-1.84-4.08-4.384Zm15.424,4.064h-2.288l-2.624-8.128h1.808l2.032,6.416,1.792-6.416h1.84l1.856,6.448,2-6.448h1.792l-2.656,8.128h-2.288l-1.616-6.112-1.648,6.112Zm13.961-1.232c1.552,0,2.672-1.184,2.672-2.832s-1.12-2.832-2.672-2.832-2.688,1.184-2.688,2.832c0,1.632,1.136,2.832,2.688,2.832Zm2.688-5.68v-1.216h1.744v8.128h-1.744v-1.216c-.688.96-1.76,1.536-3.024,1.536-2.384,0-4.08-1.84-4.08-4.384s1.696-4.384,4.08-4.384c1.264,0,2.336.576,3.024,1.536Zm4.045-1.216h1.68v1.04c.496-.832,1.28-1.328,2.208-1.328.32,0,.688.08,1.04.208v1.552c-.448-.192-.944-.256-1.264-.256-1.152,0-1.92,1.024-1.92,2.464v4.448h-1.744v-8.128Zm7.471,3.312h5.056c-.192-1.28-1.216-2.176-2.56-2.176-1.312,0-2.272.88-2.496,2.176Zm-1.808.656c0-2.496,1.824-4.288,4.352-4.288s4.32,1.792,4.32,4.304c0,.192-.016.432-.032.624l-6.848-.016c.208,1.44,1.216,2.4,2.544,2.4,1.088,0,2.096-.64,2.4-1.536l1.632.24c-.464,1.6-2.144,2.752-4,2.752-2.544,0-4.368-1.872-4.368-4.48Zm10.571-5.408c-.592,0-.992-.432-.992-1.056,0-.608.448-1.04,1.056-1.04.688,0,1.152.576,1.152,1.456,0,1.088-.56,2.24-1.328,2.752l-.864-.448c.576-.416.976-1.104.976-1.664Zm9.633,7.12c0,1.616-1.472,2.784-3.536,2.784-2.144,0-3.696-1.296-3.696-3.088h1.6c0,.96.88,1.664,2.096,1.664,1.088,0,1.872-.496,1.856-1.168,0-.848-1.264-1.056-2.08-1.168-1.904-.288-3.344-.528-3.344-2.464,0-1.488,1.424-2.576,3.264-2.576,2.096,0,3.632,1.168,3.632,2.784h-1.616c-.016-.8-.864-1.36-2.032-1.36-.88,0-1.616.48-1.616,1.136,0,.608.608.8,1.92,1.024,1.552.256,3.552.592,3.552,2.432Zm-225.253-8.552c3.59,0,6.5,2.91,6.5,6.5s-2.91,6.5-6.5,6.5-6.5-2.91-6.5-6.5,2.91-6.5,6.5-6.5Zm6-205c3.59,0,6.5,2.91,6.5,6.5s-2.91,6.5-6.5,6.5-6.5-2.91-6.5-6.5,2.91-6.5,6.5-6.5ZM121.5,106c3.59,0,6.5,2.91,6.5,6.5s-2.91,6.5-6.5,6.5-6.5-2.91-6.5-6.5,2.91-6.5,6.5-6.5Zm-23,175c3.59,0,6.5,2.91,6.5,6.5s-2.91,6.5-6.5,6.5-6.5-2.91-6.5-6.5,2.91-6.5,6.5-6.5Zm-2,204c3.59,0,6.5,2.91,6.5,6.5s-2.91,6.5-6.5,6.5-6.5-2.91-6.5-6.5,2.91-6.5,6.5-6.5Z"
                  strokeWidth="0"
                />
              </svg>
            </div>
            </ScrollReveal>
          </Col>
          <Col xxl={5} xl={6} lg={6} className="technology-right">
            <ScrollReveal direction="right" delay={0.4}>
            <Swiper
              navigation={false}
              slidesPerView={1}
              loop={true}
              // effect={"creative"}
              autoplay={{ delay: 3000 }}
              onSwiper={setSwiperRef}
              // creativeEffect={{
              //   prev: {
              //     translate: [0, -400, 0],
              //   },
              //   next: {
              //     translate: [0, "100%", 0],
              //   },
              // }}
              className="swiper-wrapper"
              modules={[Pagination, EffectCreative, Autoplay]}
              pagination={{ clickable: true }}
              onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
              breakpoints={{
                991: {
                  direction: "horizontal",
                },
                992: {
                  direction: "vertical",
                },
              }}
            >
              {Technologys.map((item, index) => (
                <SwiperSlide key={index}>
                  <div className="folder">
                    <div className="technology-item-box">
                      <div className="technology-icon-wrap d-flex align-items-center">
                        <span className={`srv-icon-bg ${item.iconClass}`}>
                          <Image
                            src={item.iconUrl}
                            alt={item.title}
                            width={28}
                            height={28}
                            className="technology-icon-img"
                          />
                        </span>
                        <h4 className="box-small-head">{item.title}</h4>
                      </div>
                      <p>{item.description}</p>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
            </ScrollReveal>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default TechnologyWeUse;
