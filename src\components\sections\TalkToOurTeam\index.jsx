"use client";
import { Container, Row, Col, Form } from "react-bootstrap";
import Image from "next/image";
import { formSubmit } from "@/app/action/action";
import { Formik } from "formik";
import { useRouter } from "next/navigation";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/bootstrap.css";
import { useRef, useState } from "react";
import { validatePhoneNumber } from "@/app/utils/validatePhoneNumber";
import AnimatedInput from "@/components/common/AnimatedInput";
import AnimatedButton from "@/components/common/AnimatedButton";
import ScrollReveal from "@/components/common/ScrollReveal";
import LoadingSpinner from "@/components/common/LoadingSpinner";

const TalkToOurTeam = ({source}) => {
  const router = useRouter();
  const [phone, setPhone] = useState("");

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    option: "",
  });

  const [formStatus, setFormStatus] = useState(false);
  const buttonRef = useRef(null);
  const textRef = useRef(null);
  return (
    <div className="section talktoteam-section">
      <Container>
        <Row className="talktoteam-wrap">
          <Col xl={6} className="talktoteam-left d-none d-xl-block">
            <ScrollReveal direction="left" delay={0.2}>
              <Image
                src="/images/talktoteam-img.png"
                alt="talk with our team"
                width={570}
                height={452}
              />
            </ScrollReveal>
          </Col>
          <Col xl={6} lg={12} className="client-feedback-right flex-1">
            <ScrollReveal direction="right" delay={0.3}>
              <div className="section-head">
                <h2 className="main-title mb-4">
                  Ready to unlock the power of location?
                </h2>
                <p>
                  Discover the power of indoor positioning and tracking - contact
                  us today!
                </p>
              </div>
            </ScrollReveal>
            <div className="talkto-form-wrap">
              <Formik
                initialValues={{
                  name: "",
                  phone_number: "",
                  option: "",
                }}
                validate={(values) => {
                  const errors = {};
                  if (!values.name) {
                    errors.name = "Name is required";
                  }
                  if (!values.phone_number) {
                    errors.phone_number = "Phone number is required";
                  } else {
                    try {
                      validatePhoneNumber(null, values.phone_number);
                    } catch (err) {
                      errors.phone_number = err.message;
                    }
                  }

                  if (!values.option) {
                    errors.option = "Please select an option";
                  }

                  return errors;
                }}
                onSubmit={async (values, { setSubmitting, resetForm }) => {
                  try {
                    const status = await formSubmit(values, `footer-${source}`);
                    if (status === 201) {
                      setFormStatus(true); // Set form status to submitted
                      router.push("/thankyou");
                      setTimeout(() => {
                        setFormStatus(false);
                        setSubmitting(false);
                        resetForm();
                      }, 5000);
                    } else {
                      console.error(
                        `Form submission failed with status: ${status}`
                      );
                    }
                  } catch (error) {
                    console.error(`Form submission failed: ${error.message}`);
                  }
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  isSubmitting,
                  setFieldValue,
                  dirty,
                  isValid,
                }) => (
                  <Form onSubmit={handleSubmit}>
                    <Row className="talkto-form-row">
                      <Col md={6}>
                        <Form.Group className="talkto-form-item  position-relative">
                          <Form.Control
                            className="form-control"
                            type="text"
                            name="name"
                            placeholder="Name"
                            onChange={handleChange}
                            onBlur={handleBlur}
                            value={values.name}
                            isInvalid={errors.name && touched.name}
                          />
                          <Form.Control.Feedback type="invalid">
                            {errors.name && touched.name && errors.name}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="talkto-form-item  position-relative">
                          <PhoneInput
                            inputProps={{
                              name: "phone_number",
                              onBlur: handleBlur,
                            }}
                            placeholder="Phone Number*"
                            country={"us"}
                            enableSearch={true}
                            onChange={(phone) =>
                              setFieldValue("phone_number", phone)
                            }
                            value={phone}
                            inputStyle={{
                              paddingLeft: "60px!important",
                            }}
                            inputClass={`w-100 ${
                              touched.phone_number && errors.phone_number
                                ? "is-invalid"
                                : ""
                            }`}
                          />
                          {errors.phone_number && touched.phone_number && (
                            <div className="invalid-feedback d-block">
                              {errors.phone_number}
                            </div>
                          )}
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Form.Group className="talkto-form-item position-relative">
                          <Form.Select
                            name="option"
                            value={values.option}
                            onChange={(e) =>
                              setFieldValue("option", e.target.value)
                            }
                            ariaLabel="Enter Option"
                            onBlur={handleBlur}
                            isInvalid={touched.option && !!errors.option}
                          >
                            <option value="">Choose...</option>
                            <option value="Indoor navigation">
                              Indoor navigation
                            </option>
                            <option value="Asset tracking">
                              Asset tracking
                            </option>
                            <option value="General Inquiry">
                              General Inquiry
                            </option>
                          </Form.Select>
                          <Form.Control.Feedback type="invalid">
                            {errors.option}
                          </Form.Control.Feedback>
                        </Form.Group>
                      </Col>
                      <Col md={12}>
                        <Button
                          variant="dark"
                          type="submit"
                          className={`form-btn ${
                            isSubmitting ? "loading" : ""
                          }`}
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <div class="loader">
                              <div class="dot-elastic"></div>
                            </div>
                          ) : formStatus ? (
                            <span>Submitted</span>
                          ) : (
                            <span>Talk to our Team</span>
                          )}
                        </Button>
                      </Col>
                    </Row>
                  </Form>
                )}
              </Formik>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default TalkToOurTeam;
