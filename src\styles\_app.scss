@font-face {
  font-family: "ObjectivityRegular";
  src: url("../assets/fonts/regular/ObjectivityRegular.eot");
  src: url("../assets/fonts/regular/ObjectivityRegular.eot")
      format("embedded-opentype"),
    url("../assets/fonts/regular/ObjectivityRegular.woff2") format("woff2"),
    url("../assets/fonts/regular/ObjectivityRegular.woff") format("woff"),
    url("../assets/fonts/regular/ObjectivityRegular.ttf") format("truetype");
}
@font-face {
  font-family: "ObjectivityExtraBold";
  src: url("../assets/fonts/extrabold/ObjectivityExtraBold.eot");
  src: url("../assets/fonts/extrabold/ObjectivityExtraBold.eot")
      format("embedded-opentype"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.woff2")
      format("woff2"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.woff") format("woff"),
    url("../assets/fonts/extrabold/ObjectivityExtraBold.ttf")
      format("truetype");
}
@font-face {
  font-family: "ObjectivityMedium";
  src: url("../assets/fonts/medium/ObjectivityMedium.eot");
  src: url("../assets/fonts/medium/ObjectivityMedium.eot")
      format("embedded-opentype"),
    url("../assets/fonts/medium/ObjectivityMedium.woff2") format("woff2"),
    url("../assets/fonts/medium/ObjectivityMedium.woff") format("woff"),
    url("../assets/fonts/medium/ObjectivityMedium.ttf") format("truetype");
}
@font-face {
  font-family: "ObjectivityBold";
  src: url("../assets/fonts/bold/ObjectivityBold.eot");
  src: url("../assets/fonts/bold/ObjectivityBold.eot")
      format("embedded-opentype"),
    url("../assets/fonts/bold/ObjectivityBold.woff2") format("woff2"),
    url("../assets/fonts/bold/ObjectivityBold.woff") format("woff"),
    url("../assets/fonts/bold/ObjectivityBold.ttf") format("truetype");
}

body {
  font-family: $font;
  color: $body-color;
  font-size: $body-font-size;
  line-height: 30px;
}
.flex-1 {
  flex: 1;
}
.main-title {
  font-family: $font-bold;
  font-size: 40px;
  line-height: 50px;
}
.section {
  padding: 110px 0;
}
.btn {
  background: #ffffff;
  box-shadow: 0px 10px 30px rgba(69, 69, 69, 0.09);
  border-radius: 100px;
  min-width: 265px;
  color: #000000;
  border: none;
  height: 50px;
  font-family: $font-medium;
  transition: all 0.3s ease;
  transform: translateZ(0);
  will-change: transform, box-shadow, background-color;
  position: relative;
  overflow: hidden;

  // Ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  &:active::before {
    width: 300px;
    height: 300px;
  }

  &.btn-secondary {
    background-color: $secondary-color;
    font-family: $font-medium;
    font-size: 15px;
    border: 2.5px solid $secondary-color;

    &:hover:not(:disabled) {
      background-color: darken($secondary-color, 8%);
      border-color: darken($secondary-color, 8%);
      transform: translateY(-2px);
      box-shadow: 0px 15px 40px rgba(255, 184, 45, 0.25);
    }
  }

  &:hover:not(:disabled),
  &:focus:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0px 15px 40px rgba(69, 69, 69, 0.15);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: 0.1s;
  }

  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }

  &.btn-dark {
    background-color: black;
    color: #fff;

    &:hover:not(:disabled) {
      background-color: #333;
      transform: translateY(-2px);
      box-shadow: 0px 15px 40px rgba(0, 0, 0, 0.25);
    }
  }

  &.btn-outline {
    border: 2.5px solid $secondary-color;
    color: $body-color;
    background-color: transparent;

    &:hover:not(:disabled) {
      background-color: $secondary-color;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0px 15px 40px rgba(255, 184, 45, 0.25);
    }
  }

  // Loading state
  &.loading {
    pointer-events: none;

    .btn-text {
      opacity: 0;
    }

    .btn-spinner {
      opacity: 1;
    }
  }
}


.border-btn {
  border: 1px solid $primary-color;
  background-color: $primary-color;
  border-radius: 100px;
  color: #fff;
  font-size: 16px;
  font-family: $font-medium;
  text-decoration: none;
  transition: all 0.3s ease;
  transform: translateZ(0);
  will-change: transform, box-shadow, background-color;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;

  // Ripple effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  &:active::before {
    width: 200px;
    height: 200px;
  }

  &.medium-btn {
    height: 36px;
    font-size: 14px;
    font-family: $font-medium;
    min-width: 100px;
    padding: 0 17px;
    line-height: 36px;
  }

  &.large-btn {
    min-width: 152px;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  &:hover:not(:disabled),
  &:focus:not(:disabled) {
    background-color: darken($primary-color, 8%);
    border-color: darken($primary-color, 8%);
    transform: translateY(-1px);
    box-shadow: 0px 10px 25px rgba(52, 168, 83, 0.25);
    text-decoration: none;
    color: #fff;
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: 0.1s;
  }

  &:focus-visible {
    outline: 2px solid darken($primary-color, 20%);
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
  }
}
.mb-60 {
  margin-bottom: 60px;
}
.secondary-bg {
  background-color: $primary-color;
}
.primary-bg {
  background-color: $secondary-color;
}
.secondary-color {
  color: $primary-color;
}
.primary-color {
  color: $secondary-color;
}
.p-relative {
    position: relative;
}
.mb-30 {
  margin-bottom: 30px;
}
.mb-40 {
  margin-bottom: 40px;
}
.mb-24 {
  margin-bottom: 24px;
}
.mt-24 {
  margin-top: 24px;
}
.no-wrap {
  flex-wrap: nowrap;
}
.double-btn {
  display: flex;
  .btn {
    margin-left: 20px;
    &:first-child {
      margin-left: 0;
    }
  }
}
.size-24 {
  font-size: 24px;
}
.size-20 {
  font-size: 20px;
}
.bold {
  font-family: $font-bold;
}
@media (max-width: 768px) {
  .section {
    padding: 30px 0;
  }
  .main-title {
    font-size: 24px;
    line-height: 30px;
  }
}

.bg-bc-light {
  background: #f3f3f3;
}
