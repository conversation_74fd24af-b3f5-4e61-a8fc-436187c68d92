"use client"
import { useEffect, useState } from 'react';
import Container from 'react-bootstrap/Container';
import Image from 'next/image';
import Headermenu from "./components/headermenu";
import HeaderSearch from "./components/headersearch";
import Link from 'next/link';
import AnimatedButton from "@/components/common/AnimatedButton";
import { motion } from 'framer-motion';
import { useReducedMotion } from '@/lib/animations';


function Header() {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isSticky, setIsSticky] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const handleScroll = () => {
    const scrollPosition = window.scrollY;
    setIsSticky(scrollPosition > 50); // Adjust the value as needed for when the header should stick
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "";

    return () => {
      document.body.style.overflow = "";
    };
  }, [menuOpen]);

  const headerVariants = {
    hidden: { y: -100, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: prefersReducedMotion ? 0 : 0.6, ease: "easeOut" }
    },
    sticky: {
      y: 0,
      opacity: 1,
      backdropFilter: "blur(10px)",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      transition: { duration: prefersReducedMotion ? 0 : 0.3 }
    }
  };

  return (
    <motion.div
      className={`header-wrap ${isSticky ? 'sticky' : ''} ${menuOpen ? 'menu-open' : ''}`}
      variants={headerVariants}
      initial="hidden"
      animate={isSticky ? "sticky" : "visible"}
    >
        <Container fluid={"xl"}>
          <div className="d-flex header-cont justify-content-between align-items-center">
            <motion.div
              className="header-left"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0 : 0.6, delay: 0.2 }}
            >
              <Link href="/">
                <Image
                  src="/images/logo.png"
                  alt="beco logo"
                  width={159}
                  height={28}
                  className="beco-logo"
                />
              </Link>
            </motion.div>
            <motion.div
              className="header-section-wrap d-flex align-items-center"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: prefersReducedMotion ? 0 : 0.6, delay: 0.3 }}
            >
            <div className={`header-menu d-flex order-lg-1 order-2 ${isSearchOpen ? 'menu-hidden' : ''}`}>
              <Headermenu menuOpen={menuOpen} setMenuOpen={setMenuOpen}  />
              </div>

              <div className="header-right d-flex order-lg-2 order-1">
              <AnimatedButton
                as={Link}
                href="/contact"
                className="border-btn medium-btn header-right-child"
                variant="outline"
              >
                Contact us
              </AnimatedButton>
                {/* <div className="language-select-wrap header-right-child">
                <span>EN</span>
              </div>
              <div className="header-right-child">
                <HeaderSearch isSearchOpen={isSearchOpen} toggleSearch={toggleSearch} />
              </div> */}
              </div>
            </motion.div>
          </div>
        </Container>
      </motion.div>
  );
}

export default Header;
