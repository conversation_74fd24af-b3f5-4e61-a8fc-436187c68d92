.header-nav {
  padding: 0;

  .dropdown {
    position: inherit;
  }

  .dropdown-menu {
    background: #ffffff;
    box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.15);
    border-radius: 9px;
    border: none;
    left: unset;
    top: 110%;
    right: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(10px);

    &.show {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }
  }

  .nav-link {
    font-size: 15px;
    font-family: $font-medium;
    color: #000;
    padding: 0 16px !important;
    position: relative;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      width: 0;
      height: 2px;
      background-color: $primary-color;
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }

    &:hover {
      color: $primary-color;

      &::after {
        width: 80%;
      }
    }

    &.active {
      color: $primary-color;

      &::after {
        width: 80%;
      }
    }
  }
}
.hover-dropdown .dropdown-menu {
  display: block; /* Hide dropdown menu by default */
}

.hover-dropdown:hover .dropdown-menu {
  display: none; /* Show dropdown menu on hover */
}
.header-search-btn {
  width: 38px;
  height: 38px;
  background-color: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translateZ(0);

  &:hover {
    background-color: darken($primary-color, 10%);
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(52, 168, 83, 0.3);
  }

  &:active {
    transform: scale(0.95);
    transition-duration: 0.1s;
  }
}
.header-cont {
  padding: 15px 0;
}
.header-right {
  // padding-left: 20px;
}
.header-right-child {
  margin-left: 28px;
  &:first-child {
    margin-left: 0;
  }
}
.language-select-wrap {
  width: 39px;
  height: 39px;
  border: 1px solid #000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 39px;
  text-align: center;
  display: block;
}

.search-container {
  position: relative;
}
.search-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
}
.search-overlay {
  position: absolute;
  top: 0;
  left: 0px;
  right: 0;
  bottom: 0;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
  display: flex;
}
.search-input {
  width: 100%;
  font-size: 16px;
  height: 40px;
  border: 1px solid rgba(0, 0, 0, 0.78);
  border-radius: 100px;
  padding: 0 15px;
}
.close-button {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  margin-left: 10px;
  width: 25px;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.header-section-wrap {
  position: relative;
  gap: 10px;
}
// .menu-hidden {
//   .header-nav {
//     display: none;
//   }
// }
.header-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  li {
    margin: 10px 0;
    .nav-link {
      display: flex;
      align-items: center;
      padding: 8px 10px !important;
      border-radius: 5px;
      &:hover {
        background-color: rgba($color: $primary-color, $alpha: 0.2);
        color: $primary-color;

        .nav-icon {
          background-color: rgba($color: $primary-color, $alpha: 0.2);
        }
      }
    }
    .nav-icon {
      width: 39px;
      height: 39px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
      background-color: rgba(239, 239, 239, 1);
      border-radius: 50%;
    }
  }
}
.hover-dropdown {
  .dropdown-toggle::after {
    display: none;
  }
}
.hover-dropdown:hover {
  .dropdown-toggle {
    position: relative;
  }

  .dropdown-toggle::after {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px;
    border-color: #fff;
    border-top-color: transparent;
    border-right-color: transparent;
    border-left-color: transparent;
    position: absolute;
    pointer-events: none;
    z-index: 2000;
    bottom: -8px;
    right: 50%;
    transform: translate(50%);
    display: block;
  }
}
.menu-dpdnarrow-wrap {
  margin-left: 7px;
}

.header-wrap {
  position: relative;
  z-index: 10;
  transition: all 0.4s ease;
  transform: translateZ(0);

  @media (max-width: 1199px) {
    padding: 0 30px;
  }

  &.sticky {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px) saturate(200%);
    -webkit-backdrop-filter: blur(10px) saturate(200%);
    border-bottom: 1px solid #e9ebed;
    box-shadow: 0px 5px 20px rgba(51, 55, 59, 0.1);
    animation: slideDown 0.4s ease-out;

    .header-cont {
      padding: 10px 0 !important;
      transition: padding 0.4s ease;
    }

    .beco-logo {
      transform: scale(0.9);
      transition: transform 0.4s ease;
    }
  }

  &.menu-open {
    background-color: #fff;
    border-bottom: 1px solid #e9ebed;
    box-shadow: 0px 2px 4px 0px rgba(51, 55, 59, 0.08),
      0px 1px 10px 0px rgba(51, 55, 59, 0.06);
  }

  @-webkit-keyframes slideDown {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideDown {
    0% {
      opacity: 0;
      transform: translateY(-100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
}

.dropdown-item {
  &.active,
  &:active,
  &:hover,
  &:focus {
    background-color: transparent;
    color: inherit;
  }
}
.navbar-toggler {
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  box-shadow: none;

  &:focus {
    box-shadow: none;
  }
  .menu-toggle {
    display: flex;
    flex-direction: column;
    width: 30px;
    cursor: pointer;
    span {
      background: #000;
      border-radius: 30px;
      height: 3px;
      margin: 3px 0;
      transition: 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);
      &:nth-of-type(1) {
        width: 50%;
      }
      &:nth-of-type(2) {
        width: 100%;
      }
      &:nth-of-type(3) {
        width: 75%;
      }
    }
  }
}

.navbar-toggler:not(.collapsed) {
  .menu-toggle {
    span {
      transition: 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);

      &:nth-of-type(1) {
        transform-origin: bottom;
        transform: rotatez(45deg) translate(4px, 0px);
      }
      &:nth-of-type(2) {
        transform-origin: top;
        transform: rotatez(-45deg);
      }
      &:nth-of-type(3) {
        transform-origin: bottom;
        width: 50%;
        transform: translate(14px, -5px) rotatez(45deg);
      }
    }
  }
}

.navbar-wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 991px) {
  .menu-collapse {
    --top-value: 66px;
    position: fixed;
    overflow: hidden;
    top: var(--top-value);
    left: 0;
    width: 100%;
    margin: 0;

    .navbar-nav {
      display: block;
      height: 0;
      background-color: #fafafb;
      overflow-y: auto;
      overflow-x: hidden;
      transition-property: height, transform;
      transition-duration: 0.25s;
      transition-timing-function: ease-in-out;
    }

    &.opened {
      .navbar-nav {
        height: calc(100vh - var(--top-value));
        transition-property: height, transform;
        transition-duration: 0.25s;
        transition-timing-function: ease-in-out;
      }
    }
  }

  .header-nav .nav-link {
    padding: 20px 45px !important;
    font-size: 16px;
    line-height: 20px;
    border-top: 1px solid #e8e8eb;
    .drp-item {
      justify-content: space-between;
      img {
        height: 18px;
        width: 18px;
      }
    }
  }
  .header-submenu li .nav-link {
    padding: 12px 60px !important;
  }
  .dropdown-item {
    padding: 0;
  }
  .dropdown-menu {
    box-shadow: none !important;
    background-color: transparent !important;
    border-radius: 0 !important;
    margin: 0;
    padding: 0;
    .custom-dropdown {
      background-color: #f1f1f1;
      .nav-link {
        border-color: #e8e8eb !important;
      }
    }
    .header-submenu li .nav-icon {
      width: 30px;
      height: 30px;
      img {
        width: 20px;
        height: 20px;
      }
    }
    .row {
      flex-wrap: wrap;
    }
    .col {
      padding: 0 !important;
      flex: 1 0 100%;
      width: 100%;
    }

    .header-submenu li {
      margin: 0 !important;
    }
  }
}
@media (max-width: 767px) {
  .menu-collapse {
    --top-value: 57px;
  }
  .header-wrap {
    padding: 0 15px;
  }
  .header-right-child {
    display: none;
  }
}

.get-demo-popup {
  backdrop-filter: blur(10px) saturate(200%);
  background-color: #ffffff52;
  .modal-content {
    background-color: $primary-color;
    border: none;
    .close-btn {
      position: absolute;
      top: 10px;
      right: 15px;
      cursor: pointer;
      outline: none;
      border: none;
      background-color: none;
      background: none;
    }
    .modal-body {
      padding: 50px 35px 20px 35px;
      border: none;
      @media (max-width: 991px) {
        padding: 50px 25px 20px 25px;
      }
    }
    .get-demo-popup-content {
      h3 {
        font-family: $font-bold;
        font-size: 32px;
        font-weight: 700;
        line-height: 37px;
        text-align: left;
        margin-bottom: 25px;
        color: #ffffff;
        @media (max-width: 1199px) {
          font-size: 27px;
        }
          @media (max-width: 991px) {
          font-size: 24px;
          line-height: 28px;
          text-align: center;
        }
      }
    }
    .talkto-form-row {
      background-color: #fff;
      padding: 40px 20px;
      border-radius: 15px;
      @media (max-width: 991px) {
        padding: 30px 10px;
      }
    }
    .talkto-form-wrap {
      input.form-control {
        height: 45px;
      }
      label {
        margin-bottom: 0px;
        font-weight: 600;
        font-size: 13px;
      }
      .form-select {
        border: 1px solid #d9cccc;
        height: 45px;
        color: #000;
        &.is-invalid {
          border: 1px solid #dc3545;
        }
      }
      button {
        box-shadow: 0px 2px 5px 0px #00000040;
        height: 50px;
      }
    }
  }
}
