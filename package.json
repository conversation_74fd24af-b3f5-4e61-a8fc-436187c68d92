{"name": "becomap.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/third-parties": "^15.0.3", "axios": "^1.7.7", "bootstrap": "^5.3.3", "canvas-confetti": "^1.9.3", "formik": "^2.4.6", "framer-motion": "^12.20.1", "libphonenumber-js": "^1.11.15", "next": "14.2.13", "next-share": "^0.27.0", "next-sitemap": "^4.2.3", "react": "^18", "react-bootstrap": "^2.10.4", "react-canvas-confetti": "^2.0.7", "react-confetti": "^6.1.0", "react-dom": "^18", "react-floating-whatsapp": "^5.0.8", "react-leaflet": "^4.2.1", "react-loading-skeleton": "^3.5.0", "react-phone-input-2": "^2.15.1", "react-spring": "^9.7.4", "sharp": "^0.33.5", "swiper": "^11.1.14", "swr": "^2.2.5"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.13", "file-loader": "^6.2.0", "sass": "^1.77.6"}}