// Reusable animated components
import { motion } from 'framer-motion';
import { useScrollAnimation, useReducedMotion } from './hooks';
import { 
  fadeInUp, 
  scrollReveal, 
  staggerContainer, 
  staggerItem,
  buttonHover,
  buttonPrimary,
  buttonSecondary
} from './variants';

/**
 * Animated container that reveals children on scroll
 */
export const AnimatedSection = ({ 
  children, 
  className = "", 
  variant = scrollReveal,
  ...props 
}) => {
  const { ref, isInView } = useScrollAnimation();
  const prefersReducedMotion = useReducedMotion();

  return (
    <motion.div
      ref={ref}
      initial={prefersReducedMotion ? "visible" : "hidden"}
      animate={isInView ? "visible" : "hidden"}
      variants={variant}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Animated container with staggered children
 */
export const StaggeredContainer = ({ 
  children, 
  className = "",
  staggerDelay = 0.1,
  ...props 
}) => {
  const { ref, isInView } = useScrollAnimation();
  const prefersReducedMotion = useReducedMotion();

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : staggerDelay,
        delayChildren: prefersReducedMotion ? 0 : 0.2
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Animated item for use within StaggeredContainer
 */
export const StaggeredItem = ({ 
  children, 
  className = "",
  variant = staggerItem,
  ...props 
}) => {
  const prefersReducedMotion = useReducedMotion();

  return (
    <motion.div
      variants={prefersReducedMotion ? { hidden: {}, visible: {} } : variant}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Animated button with hover and tap effects
 */
export const AnimatedButton = ({ 
  children, 
  variant = "primary",
  className = "",
  onClick,
  disabled = false,
  ...props 
}) => {
  const prefersReducedMotion = useReducedMotion();
  
  const getVariant = () => {
    if (prefersReducedMotion) return {};
    switch (variant) {
      case "primary":
        return buttonPrimary;
      case "secondary":
        return buttonSecondary;
      default:
        return buttonHover;
    }
  };

  return (
    <motion.button
      variants={getVariant()}
      initial="rest"
      whileHover={disabled ? "rest" : "hover"}
      whileTap={disabled ? "rest" : "tap"}
      className={className}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </motion.button>
  );
};

/**
 * Animated text that fades in word by word
 */
export const AnimatedText = ({ 
  text, 
  className = "",
  delay = 0.1,
  ...props 
}) => {
  const { ref, isInView } = useScrollAnimation();
  const prefersReducedMotion = useReducedMotion();
  
  const words = text.split(' ');

  const containerVariants = {
    hidden: {},
    visible: {
      transition: {
        staggerChildren: prefersReducedMotion ? 0 : delay
      }
    }
  };

  const wordVariants = {
    hidden: { 
      opacity: 0, 
      y: 20 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
      {...props}
    >
      {words.map((word, index) => (
        <motion.span
          key={index}
          variants={prefersReducedMotion ? { hidden: {}, visible: {} } : wordVariants}
          style={{ display: 'inline-block', marginRight: '0.25em' }}
        >
          {word}
        </motion.span>
      ))}
    </motion.div>
  );
};

/**
 * Animated card with hover effects
 */
export const AnimatedCard = ({ 
  children, 
  className = "",
  hoverScale = 1.02,
  ...props 
}) => {
  const prefersReducedMotion = useReducedMotion();

  const cardVariants = {
    rest: { 
      scale: 1,
      boxShadow: "0px 10px 30px rgba(69, 69, 69, 0.09)"
    },
    hover: { 
      scale: prefersReducedMotion ? 1 : hoverScale,
      boxShadow: "0px 20px 50px rgba(69, 69, 69, 0.15)",
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="rest"
      whileHover="hover"
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
};

/**
 * Animated loading spinner
 */
export const LoadingSpinner = ({ 
  size = 24, 
  color = "#34A853",
  className = "" 
}) => {
  const spinVariants = {
    animate: {
      rotate: 360,
      transition: {
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }
    }
  };

  return (
    <motion.div
      variants={spinVariants}
      animate="animate"
      className={className}
      style={{
        width: size,
        height: size,
        border: `2px solid ${color}20`,
        borderTop: `2px solid ${color}`,
        borderRadius: '50%',
        display: 'inline-block'
      }}
    />
  );
};

/**
 * Animated progress bar
 */
export const AnimatedProgressBar = ({ 
  progress = 0, 
  height = 4,
  backgroundColor = "#e0e0e0",
  fillColor = "#34A853",
  className = "",
  ...props 
}) => {
  return (
    <div
      className={className}
      style={{
        width: '100%',
        height: height,
        backgroundColor: backgroundColor,
        borderRadius: height / 2,
        overflow: 'hidden'
      }}
      {...props}
    >
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        style={{
          height: '100%',
          backgroundColor: fillColor,
          borderRadius: height / 2
        }}
      />
    </div>
  );
};

/**
 * Animated counter
 */
export const AnimatedCounter = ({ 
  from = 0, 
  to, 
  duration = 2,
  className = "",
  ...props 
}) => {
  const { ref, isInView } = useScrollAnimation();

  return (
    <motion.span
      ref={ref}
      className={className}
      {...props}
    >
      {isInView && (
        <motion.span
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.span
            initial={from}
            animate={to}
            transition={{ duration, ease: "easeOut" }}
            onUpdate={(latest) => {
              if (ref.current) {
                ref.current.textContent = Math.floor(latest);
              }
            }}
          />
        </motion.span>
      )}
    </motion.span>
  );
};
